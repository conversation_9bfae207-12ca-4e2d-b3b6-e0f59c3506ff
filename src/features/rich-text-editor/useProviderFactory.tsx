/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { useCallback, useLayoutEffect, useRef } from 'react';
import { Doc } from 'yjs';
import { HocuspocusProvider, HocuspocusProviderWebsocket } from '@hocuspocus/provider';
import config from '../../config';

interface ColabState {
  ws: HocuspocusProviderWebsocket,
  providerMap: Map<string, HocuspocusProvider>
}

const createWebSocketConn = (): ColabState => {
  return {
    ws: new HocuspocusProviderWebsocket({
      url: `${config.websocketUrl}/ws/collaboration`
    }),
    providerMap: new Map(),
  };
}

const getDocFromMap = (id: string, yjsDocMap: Map<string, Doc>): Doc => {
  const doc = yjsDocMap.get(id);
  if (doc) {
    doc.load();
    return doc
  }

  const newDoc = new Doc();
  yjsDocMap.set(id, newDoc);

  return newDoc;
};

export const useProviderFactory = () => {

  const ref = useRef<ColabState>();

  useLayoutEffect(() => {
    if (!ref.current) {
      ref.current = createWebSocketConn()
    }
    return () => {
      ref.current?.ws.disconnect();
    }
  }, []);


  const providerFactory = useCallback((id: string, yjsDocMap: Map<string, Doc>) => {
    if (!ref.current) {
      ref.current = createWebSocketConn();
    }

    const existingProvider = ref.current.providerMap.get(id);
    const doc = getDocFromMap(id, yjsDocMap); // Ensure it's in map, even if we have provider
    if (existingProvider) {
      return existingProvider;
    }

    const provider = new HocuspocusProvider({
      websocketProvider: ref.current.ws,
      name: id,
      document: doc,
    });
    // In v3, we need to explicitly attach when using multiplexing
    provider.attach();
    provider.on('synced', function (this: HocuspocusProvider, data: { state: boolean }) {
      // Lexical expects this event to be emitted to initialize the editor state
      // provider.on('sync', onSync); ->  initializeEditor(editor, initialEditorState) in CollaborationPlugin
      this.emit('sync', data.state);
    });

    ref.current.providerMap.set(id, provider);
    return provider as any;
  }, []);

  return { providerFactory };
}
